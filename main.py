import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import List, Dict, Optional

# --- Настройки визуализации ---
def set_plot_style():
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams.update({
        'figure.figsize': (13, 6.5),
        'axes.titlesize': 15,
        'axes.titleweight': 'bold',
        'axes.titlepad': 20,
        'axes.labelsize': 12,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'legend.fontsize': 11,
        'grid.alpha': 0.3,
        'lines.markersize': 7,
        'lines.linewidth': 2.5
    })

# --- Загрузка и подготовка данных ---
def load_and_prepare_data(filepath: str) -> pd.DataFrame:
    try:
        df = pd.read_excel(filepath)
        for col in ['trip_time', 'order_time', 'offer_time', 'assign_time', 'arrive_time']:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce', dayfirst=True)
        df['day_order'] = df['order_time'].dt.day
        return df
    except Exception as e:
        print(f"Ошибка при загрузке данных: {e}")
        return pd.DataFrame()

# --- Алгоритм выделения отложенных заказов ---
def mark_delayed_orders(df: pd.DataFrame, threshold_min: int = 10) -> pd.DataFrame:
    # threshold_min — минимальное количество минут между заказом и подачей, чтобы считать заказ отложенным
    df = df.copy()
    if 'order_time' in df.columns and 'assign_time' in df.columns:
        df['delay_min'] = (df['assign_time'] - df['order_time']).dt.total_seconds() / 60
        df['is_delayed'] = df['delay_min'] > threshold_min
    else:
        df['is_delayed'] = False
    return df

# --- Агрегация и расчет конверсий ---
def aggregate_metrics(df: pd.DataFrame, segment_col: Optional[str] = None) -> pd.DataFrame:
    group_cols = ['day_order', 'city']
    if segment_col:
        group_cols.append(segment_col)
    agg = df.groupby(group_cols, as_index=False).agg(
        cnt_order=('id_order', 'count'),
        cnt_offer=('offer_time', 'count'),
        cnt_assign=('assign_time', 'count'),
        cnt_arrive=('arrive_time', 'count'),
        cnt_trip=('trip_time', 'count')
    )
    for a, b, name in [
        ('cnt_trip', 'cnt_order', 'order2trip'),
        ('cnt_offer', 'cnt_order', 'order2offer'),
        ('cnt_assign', 'cnt_offer', 'offer2assign'),
        ('cnt_arrive', 'cnt_assign', 'assign2arrive'),
        ('cnt_trip', 'cnt_arrive', 'arrive2trip')
    ]:
        agg[name] = np.where(agg[b] > 0, agg[a] / agg[b], np.nan)
    return agg

# --- Алерт по выбросам ---
def alert_outliers(df: pd.DataFrame, metric_cols: List[str], threshold: float = 2.5):
    alerts = []
    for col in metric_cols:
        if col in df.columns:
            med = df[col].median()
            mad = np.median(np.abs(df[col] - med))
            if mad == 0:
                continue
            outliers = df[np.abs(df[col] - med) > threshold * mad]
            for _, row in outliers.iterrows():
                alerts.append(f"Выброс по {col}: {row.to_dict()}")
    if alerts:
        print("\nАЛЕРТЫ ПО ВЫБРОСАМ:")
        for alert in alerts:
            print(alert)

# --- Визуализация ---
def plot_metric(df: pd.DataFrame, metric_col: str, title: str, ylim: tuple = (0, 100), cities: List[str] = None, segment_col: Optional[str] = None):
    set_plot_style()
    if cities is None:
        cities = df['city'].unique()
    fig, ax = plt.subplots()
    colors = plt.cm.get_cmap('tab10', len(cities))
    if segment_col and segment_col in df.columns:
        segments = df[segment_col].unique()
        for i, city in enumerate(cities):
            for seg in segments:
                data = df[(df['city'] == city) & (df[segment_col] == seg)].sort_values('day_order')
                label = f"{city} - {'Отложенный' if seg else 'Срочный'}"
                style = dict(marker='o', alpha=0.85 if seg else 0.55, color=colors(i), linewidth=2.5 if seg else 1.5)
                ax.plot(data['day_order'], data[metric_col]*100, label=label, **style)
                # Добавим подписи к точкам
                for x, y in zip(data['day_order'], data[metric_col]*100):
                    ax.annotate(f"{y:.1f}", (x, y), textcoords="offset points", xytext=(0,5), ha='center', fontsize=9, color=colors(i))
    else:
        for i, city in enumerate(cities):
            data = df[df['city'] == city].sort_values('day_order')
            ax.plot(data['day_order'], data[metric_col]*100, label=city, marker='o', color=colors(i), linewidth=2.5)
            for x, y in zip(data['day_order'], data[metric_col]*100):
                ax.annotate(f"{y:.1f}", (x, y), textcoords="offset points", xytext=(0,5), ha='center', fontsize=9, color=colors(i))
    ax.set_title(title)
    ax.set_xlabel("День месяца", fontsize=13)
    ax.set_ylabel("Конверсия, %", fontsize=13)
    ax.set_ylim(ylim)
    ax.set_xticks(sorted(df['day_order'].unique()))
    ax.grid(True, linestyle='--', alpha=0.5, which='both')
    # Легенду переносим за пределы графика справа
    ax.legend(title='Город/Тип заказа', bbox_to_anchor=(1.04, 1), loc='upper left', borderaxespad=0., frameon=True)
    # Добавим вспомогательные горизонтальные линии
    for y in range(10, 100, 10):
        ax.axhline(y, color='gray', linestyle=':', linewidth=0.7, alpha=0.2)
    plt.tight_layout(rect=[0, 0, 0.82, 1])
    plt.show()

def plot_all_metrics(df: pd.DataFrame, cities: List[str] = None, segment_col: Optional[str] = None):
    metrics = {
        'order2trip': 'Order2Trip - Базовая конверсия, %',
        'order2offer': 'Order2Offer - Конверсия из заказа в предложение водителю, %',
        'offer2assign': 'Offer2Assign - Конверсия из предложения в назначение водителя, %',
        'assign2arrive': 'Assign2Arrive - Конверсия из назначения в прибытие в т. А, %',
        'arrive2trip': 'Arrive2Trip - Конверсия из прибытия в т. А в т. Б, %'
    }
    for metric, title in metrics.items():
        plot_metric(df, metric, title, ylim=(0, 100), cities=cities, segment_col=segment_col)

# --- Основной сценарий ---
def main():
    df = load_and_prepare_data('taxi_data.xlsx')
    if df.empty:
        return
    df = mark_delayed_orders(df, threshold_min=10)
    agg = aggregate_metrics(df, segment_col='is_delayed')
    alert_outliers(agg, ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip'])
    plot_all_metrics(agg, segment_col='is_delayed')

if __name__ == "__main__":
    main()
